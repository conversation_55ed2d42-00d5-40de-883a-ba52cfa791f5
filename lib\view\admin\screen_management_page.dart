import 'package:flutter/material.dart';
import 'package:get/get.dart' hide ScreenType;
import 'package:google_fonts/google_fonts.dart';
import '../../models/screen_model.dart';
import '../../models/theater_model.dart';
import '../../controllers/screen_controller.dart';
import 'add_edit_screen_page.dart';
import 'screen_detail_page.dart';
import 'import_data_page.dart';

class ScreenManagementPage extends StatefulWidget {
  final String? theaterId; // Made optional for all screens view

  const ScreenManagementPage({Key? key, this.theaterId}) : super(key: key);

  @override
  State<ScreenManagementPage> createState() => _ScreenManagementPageState();
}

class _ScreenManagementPageState extends State<ScreenManagementPage> {
  final ScreenController _screenController = Get.put(ScreenController());
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initializeData();
    _searchController.addListener(() {
      _screenController.setSearchQuery(_searchController.text);
    });
  }

  void _initializeData() {
    if (widget.theaterId != null) {
      _screenController.loadScreensByTheater(widget.theaterId!);
    } else {
      _screenController.loadAllScreens();
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        height: MediaQuery.of(context).size.height,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xff2B5876),
              Color(0xff4E4376),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // App Bar
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                    ),
                    Expanded(
                      child: Text(
                        'Quản Lý Phòng Chiếu',
                        style: GoogleFonts.mulish(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => _showImportMenu(),
                      icon: const Icon(Icons.upload_file, color: Colors.white),
                    ),
                    IconButton(
                      onPressed: () {
                        Get.to(() => AddEditScreenPage(
                              preSelectedTheaterId: widget.theaterId,
                            ));
                      },
                      icon: const Icon(Icons.add, color: Colors.white),
                    ),
                  ],
                ),
              ),

              // Search and Filter
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  children: [
                    // Search Bar
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: TextField(
                        controller: _searchController,
                        style: const TextStyle(color: Colors.white),
                        decoration: InputDecoration(
                          hintText: 'Tìm kiếm phòng chiếu...',
                          hintStyle:
                              TextStyle(color: Colors.white.withOpacity(0.7)),
                          prefixIcon: Icon(Icons.search,
                              color: Colors.white.withOpacity(0.7)),
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.all(16),
                        ),
                      ),
                    ),
                    const SizedBox(height: 12),

                    // Type Filter
                    Obx(
                      () => Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<ScreenType?>(
                            value: _screenController.selectedType.value,
                            hint: Text(
                              'Chọn loại phòng',
                              style: TextStyle(
                                  color: Colors.white.withOpacity(0.7)),
                            ),
                            dropdownColor: const Color(0xff2B5876),
                            style: const TextStyle(color: Colors.white),
                            items: [
                              DropdownMenuItem<ScreenType?>(
                                value: null,
                                child: Text(
                                  'Tất cả loại phòng',
                                  style:
                                      GoogleFonts.mulish(color: Colors.white),
                                ),
                              ),
                              ...ScreenType.values
                                  .map((type) => DropdownMenuItem<ScreenType?>(
                                        value: type,
                                        child: Text(
                                          type.displayName,
                                          style: GoogleFonts.mulish(
                                              color: Colors.white),
                                        ),
                                      )),
                            ],
                            onChanged: (value) {
                              _screenController.setSelectedType(value);
                            },
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 12),

                    // Filter Controls Row
                    Row(
                      children: [
                        // Show All Toggle
                        Expanded(
                          child: Obx(() => GestureDetector(
                                onTap: () =>
                                    _screenController.toggleShowActiveOnly(),
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16, vertical: 12),
                                  decoration: BoxDecoration(
                                    color:
                                        _screenController.showActiveOnly.value
                                            ? Colors.white.withOpacity(0.1)
                                            : Colors.blue.withOpacity(0.3),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color:
                                          _screenController.showActiveOnly.value
                                              ? Colors.white.withOpacity(0.3)
                                              : Colors.blue,
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        _screenController.showActiveOnly.value
                                            ? Icons.visibility_off
                                            : Icons.visibility,
                                        color: Colors.white,
                                        size: 16,
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        _screenController.showActiveOnly.value
                                            ? 'Chỉ hoạt động'
                                            : 'Hiển thị tất cả',
                                        style: GoogleFonts.mulish(
                                          color: Colors.white,
                                          fontSize: 12,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              )),
                        ),
                        const SizedBox(width: 12),

                        // Reload Button
                        GestureDetector(
                          onTap: () => _screenController.refreshData(),
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                  color: Colors.white.withOpacity(0.3)),
                            ),
                            child: const Icon(
                              Icons.refresh,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Screens List
              Expanded(
                child: Obx(() {
                  if (_screenController.isLoading.value) {
                    return const Center(
                      child: CircularProgressIndicator(color: Colors.white),
                    );
                  }

                  if (_screenController.filteredScreens.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.tv_outlined,
                            size: 64,
                            color: Colors.white.withOpacity(0.5),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Không có phòng chiếu nào',
                            style: GoogleFonts.mulish(
                              fontSize: 18,
                              color: Colors.white.withOpacity(0.7),
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  return RefreshIndicator(
                    onRefresh: () => _screenController.refreshData(),
                    child: ListView.builder(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      itemCount: _screenController.filteredScreens.length,
                      itemBuilder: (context, index) {
                        final screen = _screenController.filteredScreens[index];
                        return _buildScreenCard(screen);
                      },
                    ),
                  );
                }),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildScreenCard(ScreenModel screen) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                screen.type.color.withOpacity(0.3),
                screen.type.color.withOpacity(0.1),
              ],
            ),
            borderRadius: BorderRadius.circular(25),
          ),
          child: Icon(
            screen.type.icon,
            color: screen.type.color,
            size: 24,
          ),
        ),
        title: Text(
          screen.name,
          style: GoogleFonts.mulish(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Row(
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: screen.type.color.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                    border:
                        Border.all(color: screen.type.color.withOpacity(0.5)),
                  ),
                  child: Text(
                    screen.type.displayName,
                    style: GoogleFonts.mulish(
                      fontSize: 10,
                      color: screen.type.color,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  '${screen.totalSeats} ghế',
                  style: GoogleFonts.mulish(
                    fontSize: 12,
                    color: Colors.white.withOpacity(0.7),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            if (screen.amenities.isNotEmpty)
              Wrap(
                spacing: 4,
                children: screen.amenities
                    .take(2)
                    .map(
                      (amenity) => Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.blue.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(8),
                          border:
                              Border.all(color: Colors.blue.withOpacity(0.5)),
                        ),
                        child: Text(
                          _getAmenityDisplayName(amenity),
                          style: GoogleFonts.mulish(
                            fontSize: 10,
                            color: Colors.blue[200],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    )
                    .toList(),
              ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: screen.isActive
                    ? Colors.green.withOpacity(0.2)
                    : Colors.red.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: screen.isActive ? Colors.green : Colors.red,
                ),
              ),
              child: Text(
                screen.isActive ? 'Hoạt động' : 'Tạm dừng',
                style: GoogleFonts.mulish(
                  fontSize: 10,
                  color: screen.isActive ? Colors.green : Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 8),
            const Icon(Icons.arrow_forward_ios, color: Colors.white, size: 16),
          ],
        ),
        onTap: () {
          Get.to(() => ScreenDetailPage(screen: screen));
        },
      ),
    );
  }

  String _getAmenityDisplayName(String amenity) {
    switch (amenity) {
      case 'air_conditioning':
        return 'Điều hòa';
      case 'dolby_atmos':
        return 'Dolby Atmos';
      case 'reclining_seats':
        return 'Ghế nằm';
      default:
        return amenity;
    }
  }

  void _showImportMenu() {
    Get.to(() => const ImportDataPage(dataType: ImportDataType.screen))
        ?.then((_) {
      // Refresh screens list after import
      _screenController.refreshData();
    });
  }
}
