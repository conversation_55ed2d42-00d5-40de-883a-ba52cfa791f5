import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:file_picker/file_picker.dart' as file_picker;
import '../../services/import_service.dart';
import '../../models/import_template.dart';

enum ImportDataType { movie, theater, screen }

class ImportDataPage extends StatefulWidget {
  final ImportDataType dataType;

  const ImportDataPage({Key? key, required this.dataType}) : super(key: key);

  @override
  State<ImportDataPage> createState() => _ImportDataPageState();
}

class _ImportDataPageState extends State<ImportDataPage> {
  final ImportService _importService = ImportService();

  File? selectedFile;
  List<Map<String, dynamic>> previewData = [];
  bool isLoading = false;
  bool isImporting = false;
  String? errorMessage;
  ImportResult? importResult;

  String get pageTitle {
    switch (widget.dataType) {
      case ImportDataType.movie:
        return 'Import Phim';
      case ImportDataType.theater:
        return 'Import Rạp Chiếu';
      case ImportDataType.screen:
        return 'Import Phòng Chiếu';
    }
  }

  List<String> get requiredFields {
    switch (widget.dataType) {
      case ImportDataType.movie:
        return MovieImportTemplate.requiredFields;
      case ImportDataType.theater:
        return TheaterImportTemplate.requiredFields;
      case ImportDataType.screen:
        return ScreenImportTemplate.requiredFields;
    }
  }

  List<String> get allFields {
    switch (widget.dataType) {
      case ImportDataType.movie:
        return MovieImportTemplate.allFields;
      case ImportDataType.theater:
        return TheaterImportTemplate.allFields;
      case ImportDataType.screen:
        return ScreenImportTemplate.allFields;
    }
  }

  Map<String, String> get fieldDescriptions {
    switch (widget.dataType) {
      case ImportDataType.movie:
        return MovieImportTemplate.fieldDescriptions;
      case ImportDataType.theater:
        return TheaterImportTemplate.fieldDescriptions;
      case ImportDataType.screen:
        return ScreenImportTemplate.fieldDescriptions;
    }
  }

  List<Map<String, dynamic>> get sampleData {
    switch (widget.dataType) {
      case ImportDataType.movie:
        return MovieImportTemplate.getSampleData();
      case ImportDataType.theater:
        return TheaterImportTemplate.getSampleData();
      case ImportDataType.screen:
        return ScreenImportTemplate.getSampleData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1a1a2e),
      body: SafeArea(
        child: Column(
          children: [
            // App Bar
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  IconButton(
                    onPressed: () => Get.back(),
                    icon: const Icon(Icons.arrow_back, color: Colors.white),
                  ),
                  Expanded(
                    child: Text(
                      pageTitle,
                      style: GoogleFonts.mulish(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: _showTemplateInfo,
                    icon: const Icon(Icons.info_outline, color: Colors.white),
                  ),
                ],
              ),
            ),

            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // File Selection Card
                    _buildFileSelectionCard(),
                    const SizedBox(height: 16),

                    // Preview Card
                    if (previewData.isNotEmpty) ...[
                      _buildPreviewCard(),
                      const SizedBox(height: 16),
                    ],

                    // Import Result Card
                    if (importResult != null) ...[
                      _buildResultCard(),
                      const SizedBox(height: 16),
                    ],

                    // Error Message
                    if (errorMessage != null) ...[
                      _buildErrorCard(),
                      const SizedBox(height: 16),
                    ],
                  ],
                ),
              ),
            ),

            // Bottom Actions
            if (selectedFile != null &&
                previewData.isNotEmpty &&
                importResult == null)
              Container(
                padding: const EdgeInsets.all(16),
                child: SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: isImporting ? null : _performImport,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: isImporting
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(
                            'Bắt đầu Import (${previewData.length} mục)',
                            style: GoogleFonts.mulish(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildFileSelectionCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF16213e),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Chọn File Import',
            style: GoogleFonts.mulish(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Hỗ trợ: Excel (.xlsx, .xls), CSV (.csv), JSON (.json)',
            style: GoogleFonts.mulish(
              fontSize: 12,
              color: Colors.grey[400],
            ),
          ),
          const SizedBox(height: 16),
          if (selectedFile == null) ...[
            GestureDetector(
              onTap: _pickFile,
              child: Container(
                width: double.infinity,
                height: 120,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Colors.blue.withOpacity(0.5),
                    style: BorderStyle.solid,
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.cloud_upload_outlined,
                      size: 40,
                      color: Colors.blue[300],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Nhấn để chọn file',
                      style: GoogleFonts.mulish(
                        fontSize: 14,
                        color: Colors.blue[300],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ] else ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.insert_drive_file,
                    color: Colors.green[300],
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          selectedFile!.path.split('/').last,
                          style: GoogleFonts.mulish(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          '${(selectedFile!.lengthSync() / 1024).toStringAsFixed(1)} KB',
                          style: GoogleFonts.mulish(
                            fontSize: 12,
                            color: Colors.grey[400],
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: _clearFile,
                    icon: Icon(
                      Icons.close,
                      color: Colors.red[300],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: isLoading ? null : _parseFile,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: isLoading
                    ? const SizedBox(
                        height: 16,
                        width: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        'Phân tích file',
                        style: GoogleFonts.mulish(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPreviewCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF16213e),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.preview,
                color: Colors.blue[300],
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Xem trước dữ liệu (${previewData.length} mục)',
                style: GoogleFonts.mulish(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Preview table
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.withOpacity(0.3)),
              borderRadius: BorderRadius.circular(8),
            ),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                headingRowColor:
                    WidgetStateProperty.all(Colors.grey.withOpacity(0.1)),
                columns: allFields
                    .take(5)
                    .map((field) => DataColumn(
                          label: Text(
                            field,
                            style: GoogleFonts.mulish(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ))
                    .toList(),
                rows: previewData
                    .take(5)
                    .map((row) => DataRow(
                          cells: allFields
                              .take(5)
                              .map((field) => DataCell(
                                    Text(
                                      row[field]?.toString() ?? '',
                                      style: GoogleFonts.mulish(
                                        fontSize: 11,
                                        color: Colors.grey[300],
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ))
                              .toList(),
                        ))
                    .toList(),
              ),
            ),
          ),

          if (previewData.length > 5) ...[
            const SizedBox(height: 8),
            Text(
              'Và ${previewData.length - 5} mục khác...',
              style: GoogleFonts.mulish(
                fontSize: 12,
                color: Colors.grey[400],
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildResultCard() {
    final result = importResult!;
    final isSuccess = !result.hasErrors;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF16213e),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSuccess
              ? Colors.green.withOpacity(0.5)
              : Colors.orange.withOpacity(0.5),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isSuccess ? Icons.check_circle : Icons.warning,
                color: isSuccess ? Colors.green[300] : Colors.orange[300],
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Kết quả Import',
                style: GoogleFonts.mulish(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Statistics
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Thành công',
                  result.successCount.toString(),
                  Colors.green,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Lỗi',
                  result.errorCount.toString(),
                  Colors.red,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Tổng cộng',
                  result.totalProcessed.toString(),
                  Colors.blue,
                ),
              ),
            ],
          ),

          if (result.hasErrors) ...[
            const SizedBox(height: 16),
            Text(
              'Chi tiết lỗi:',
              style: GoogleFonts.mulish(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              constraints: const BoxConstraints(maxHeight: 200),
              child: SingleChildScrollView(
                child: Column(
                  children: result.errors
                      .map((error) => Container(
                            margin: const EdgeInsets.only(bottom: 4),
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.red.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(
                                  color: Colors.red.withOpacity(0.3)),
                            ),
                            child: Text(
                              error.toString(),
                              style: GoogleFonts.mulish(
                                fontSize: 11,
                                color: Colors.red[300],
                              ),
                            ),
                          ))
                      .toList(),
                ),
              ),
            ),
          ],

          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _resetImport,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Import file khác',
                style: GoogleFonts.mulish(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: GoogleFonts.mulish(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: GoogleFonts.mulish(
              fontSize: 12,
              color: Colors.grey[400],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF16213e),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red.withOpacity(0.5)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.error,
                color: Colors.red[300],
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Lỗi',
                style: GoogleFonts.mulish(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            errorMessage!,
            style: GoogleFonts.mulish(
              fontSize: 14,
              color: Colors.red[300],
            ),
          ),
        ],
      ),
    );
  }

  // File handling methods
  Future<void> _pickFile() async {
    try {
      file_picker.FilePickerResult? result =
          await file_picker.FilePicker.platform.pickFiles(
        type: file_picker.FileType.custom,
        allowedExtensions: ['xlsx', 'xls', 'csv', 'json'],
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        setState(() {
          selectedFile = File(result.files.single.path!);
          previewData.clear();
          errorMessage = null;
          importResult = null;
        });
      }
    } catch (e) {
      setState(() {
        errorMessage = 'Lỗi khi chọn file: $e';
      });
    }
  }

  void _clearFile() {
    setState(() {
      selectedFile = null;
      previewData.clear();
      errorMessage = null;
      importResult = null;
    });
  }

  Future<void> _parseFile() async {
    if (selectedFile == null) return;

    setState(() {
      isLoading = true;
      errorMessage = null;
    });

    try {
      final data = await _importService.parseFile(selectedFile!);
      setState(() {
        previewData = data;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        errorMessage = 'Lỗi khi phân tích file: $e';
        isLoading = false;
      });
    }
  }

  Future<void> _performImport() async {
    if (previewData.isEmpty) return;

    setState(() {
      isImporting = true;
      errorMessage = null;
    });

    try {
      ImportResult result;

      switch (widget.dataType) {
        case ImportDataType.movie:
          result = await _importService.importMovies(previewData);
          break;
        case ImportDataType.theater:
          result = await _importService.importTheaters(previewData);
          break;
        case ImportDataType.screen:
          result = await _importService.importScreens(previewData);
          break;
      }

      setState(() {
        importResult = result;
        isImporting = false;
      });

      // Show success message
      Get.snackbar(
        'Import hoàn tất',
        'Đã import thành công ${result.successCount}/${result.totalProcessed} mục',
        backgroundColor: result.hasErrors ? Colors.orange : Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      setState(() {
        errorMessage = 'Lỗi khi import: $e';
        isImporting = false;
      });
    }
  }

  void _resetImport() {
    setState(() {
      selectedFile = null;
      previewData.clear();
      errorMessage = null;
      importResult = null;
    });
  }

  void _showTemplateInfo() {
    Get.dialog(
      Dialog(
        backgroundColor: const Color(0xFF16213e),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Container(
          constraints: const BoxConstraints(maxWidth: 600, maxHeight: 500),
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Colors.blue[300],
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Hướng dẫn Import',
                    style: GoogleFonts.mulish(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Get.back(),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Các trường bắt buộc:',
                        style: GoogleFonts.mulish(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 8),
                      ...requiredFields.map((field) => Padding(
                            padding: const EdgeInsets.only(bottom: 4),
                            child: Text(
                              '• ${fieldDescriptions[field] ?? field}',
                              style: GoogleFonts.mulish(
                                fontSize: 12,
                                color: Colors.red[300],
                              ),
                            ),
                          )),
                      const SizedBox(height: 16),
                      Text(
                        'Các trường tùy chọn:',
                        style: GoogleFonts.mulish(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 8),
                      ...allFields
                          .where((field) => !requiredFields.contains(field))
                          .map((field) => Padding(
                                padding: const EdgeInsets.only(bottom: 4),
                                child: Text(
                                  '• ${fieldDescriptions[field] ?? field}',
                                  style: GoogleFonts.mulish(
                                    fontSize: 12,
                                    color: Colors.grey[400],
                                  ),
                                ),
                              )),
                      const SizedBox(height: 16),
                      Text(
                        'Dữ liệu mẫu:',
                        style: GoogleFonts.mulish(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.grey.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border:
                              Border.all(color: Colors.grey.withOpacity(0.3)),
                        ),
                        child: Text(
                          _generateSampleDataText(),
                          style: GoogleFonts.sourceCodePro(
                            fontSize: 10,
                            color: Colors.grey[300],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _generateSampleDataText() {
    if (sampleData.isEmpty) return 'Không có dữ liệu mẫu';

    final sample = sampleData.first;
    final lines = <String>[];

    sample.forEach((key, value) {
      lines.add('$key: $value');
    });

    return lines.join('\n');
  }
}
