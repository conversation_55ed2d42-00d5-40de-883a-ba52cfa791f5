import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:movie_finder/controllers/schedule_controller.dart';
import 'package:movie_finder/models/showtime_model.dart';
import 'package:movie_finder/view/admin/schedule_form_page.dart';

class ScheduleManagementPage extends StatefulWidget {
  const ScheduleManagementPage({Key? key}) : super(key: key);

  @override
  State<ScheduleManagementPage> createState() => _ScheduleManagementPageState();
}

class _ScheduleManagementPageState extends State<ScheduleManagementPage> {
  final ScheduleController _scheduleController = Get.find<ScheduleController>();

  @override
  void initState() {
    super.initState();
    _scheduleController.loadInitialData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xff1a1a2e),
      appBar: AppBar(
        title: Text(
          '<PERSON>uản lý lịch chiếu',
          style: GoogleFonts.mulish(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xff16213e),
        iconTheme: const IconThemeData(color: Colors.white),
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () => _scheduleController.loadAllShowtimes(),
            icon: const Icon(Icons.refresh, color: Colors.white),
          ),
        ],
      ),
      body: Column(
        children: [
          // Header with add button
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Danh sách lịch chiếu',
                  style: GoogleFonts.mulish(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () => _navigateToCreateSchedule(),
                  icon: const Icon(Icons.add),
                  label: Text(
                    'Thêm mới',
                    style: GoogleFonts.mulish(fontWeight: FontWeight.bold),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ),

          // Schedule list
          Expanded(
            child: Obx(() {
              if (_scheduleController.isLoading.value) {
                return const Center(
                  child: CircularProgressIndicator(color: Colors.white),
                );
              }

              if (_scheduleController.showtimes.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.schedule,
                        size: 64,
                        color: Colors.white.withOpacity(0.5),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Chưa có lịch chiếu nào',
                        style: GoogleFonts.mulish(
                          color: Colors.white.withOpacity(0.7),
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton.icon(
                        onPressed: () => _navigateToCreateSchedule(),
                        icon: const Icon(Icons.add),
                        label: Text(
                          'Tạo lịch chiếu đầu tiên',
                          style: GoogleFonts.mulish(),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                );
              }

              return ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: _scheduleController.showtimes.length,
                itemBuilder: (context, index) {
                  final showtime = _scheduleController.showtimes[index];
                  return _buildShowtimeCard(showtime);
                },
              );
            }),
          ),
        ],
      ),
    );
  }

  // Navigation methods
  void _navigateToCreateSchedule() {
    Get.to(() => const ScheduleFormPage());
  }

  void _navigateToEditSchedule(ShowtimeModel showtime) {
    Get.to(() => ScheduleFormPage(showtime: showtime));
  }

  // Build showtime card
  Widget _buildShowtimeCard(ShowtimeModel showtime) {
    final movie = _scheduleController.getMovieById(showtime.movieId);
    final theater = _scheduleController.getTheaterById(showtime.theaterId);
    final screen = _scheduleController.getScreenById(showtime.screenId);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with movie title and status
            Row(
              children: [
                Expanded(
                  child: Text(
                    movie?.title ?? 'Phim không tìm thấy',
                    style: GoogleFonts.mulish(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
                _buildStatusChip(showtime.status),
              ],
            ),
            const SizedBox(height: 8),

            // Theater and screen info
            Row(
              children: [
                Icon(
                  Icons.local_movies,
                  color: Colors.white.withOpacity(0.7),
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '${theater?.name ?? 'Rạp không tìm thấy'} • ${screen?.name ?? 'Phòng không tìm thấy'}',
                    style: GoogleFonts.mulish(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Date and time info
            Row(
              children: [
                Icon(
                  Icons.schedule,
                  color: Colors.white.withOpacity(0.7),
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  '${showtime.date} • ${showtime.time} - ${showtime.endTime}',
                  style: GoogleFonts.mulish(
                    color: Colors.white.withOpacity(0.7),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Seats info
            Row(
              children: [
                Icon(
                  Icons.event_seat,
                  color: Colors.white.withOpacity(0.7),
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  'Còn ${showtime.availableSeats} ghế trống',
                  style: GoogleFonts.mulish(
                    color: Colors.white.withOpacity(0.7),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: () => _navigateToEditSchedule(showtime),
                  icon: const Icon(Icons.edit, size: 16),
                  label: Text(
                    'Sửa',
                    style: GoogleFonts.mulish(fontWeight: FontWeight.w500),
                  ),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.blue,
                  ),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  onPressed: () => _showDeleteConfirmation(showtime),
                  icon: const Icon(Icons.delete, size: 16),
                  label: Text(
                    'Xóa',
                    style: GoogleFonts.mulish(fontWeight: FontWeight.w500),
                  ),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.red,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(ShowtimeStatus status) {
    Color color;
    String text;

    switch (status) {
      case ShowtimeStatus.active:
        color = Colors.green;
        text = 'Hoạt động';
        break;
      case ShowtimeStatus.cancelled:
        color = Colors.red;
        text = 'Đã hủy';
        break;
      case ShowtimeStatus.full:
        color = Colors.orange;
        text = 'Hết chỗ';
        break;
      case ShowtimeStatus.ended:
        color = Colors.grey;
        text = 'Đã kết thúc';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.5)),
      ),
      child: Text(
        text,
        style: GoogleFonts.mulish(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  void _showDeleteConfirmation(ShowtimeModel showtime) {
    Get.dialog(
      AlertDialog(
        backgroundColor: const Color(0xff16213e),
        title: Text(
          'Xác nhận xóa',
          style: GoogleFonts.mulish(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          'Bạn có chắc chắn muốn xóa lịch chiếu này không?',
          style: GoogleFonts.mulish(color: Colors.white),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              'Hủy',
              style: GoogleFonts.mulish(color: Colors.grey),
            ),
          ),
          TextButton(
            onPressed: () async {
              Get.back();
              final success =
                  await _scheduleController.deleteShowtime(showtime.id);
              if (success) {
                Get.snackbar(
                  'Thành công',
                  'Đã xóa lịch chiếu',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.green,
                  colorText: Colors.white,
                );
              }
            },
            child: Text(
              'Xóa',
              style: GoogleFonts.mulish(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}
